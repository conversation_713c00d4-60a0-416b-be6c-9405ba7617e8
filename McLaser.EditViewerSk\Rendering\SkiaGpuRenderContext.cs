using System;
using System.Collections.Generic;
using SkiaSharp;
using McLaser.EditViewerSk.Entitys;
using McLaser.EditViewerSk.Interfaces;
using <PERSON><PERSON><PERSON>ase<PERSON>;
using System.Windows.Media;

namespace McLaser.EditViewerSk.Rendering
{
    /// <summary>
    /// 基于SkiaSharp GPU后端的渲染上下文
    /// </summary>
    public class SkiaGpuRenderContext : IDisposable
    {
        private GRContext _grContext;
        private SKSurface _surface;
        private GRBackendRenderTarget _renderTarget;
        private int _width;
        private int _height;
        private bool _isInitialized;

        /// <summary>
        /// GPU渲染是否可用
        /// </summary>
        public bool IsGpuRenderingAvailable => _isInitialized && _grContext != null;

        /// <summary>
        /// 获取GPU画布
        /// </summary>
        public SKCanvas Canvas => _surface?.Canvas;

        /// <summary>
        /// 初始化GPU渲染上下文
        /// </summary>
        public bool Initialize(int width, int height)
        {
            try
            {
                _width = width;
                _height = height;
            
                // 检查系统是否支持硬件加速
                if (!CheckHardwareAccelerationSupport())
                {
                    System.Diagnostics.Debug.WriteLine("GPU渲染: 系统不支持硬件加速");
                    return false;
                }

                // 尝试创建GPU上下文
                _grContext = CreateGpuContextWithFallback();
                if (_grContext == null)
                {
                    System.Diagnostics.Debug.WriteLine("GPU渲染: 无法创建OpenGL上下文，可能原因：");
                    System.Diagnostics.Debug.WriteLine("  1. OpenGL驱动程序过旧或不兼容");
                    System.Diagnostics.Debug.WriteLine("  2. 当前线程没有有效的OpenGL上下文");
                    System.Diagnostics.Debug.WriteLine("  3. 硬件不支持或被禁用");
                    System.Diagnostics.Debug.WriteLine("  4. 在虚拟机或远程桌面环境中运行");
                    return false;
                }

                // 创建渲染目标
                var framebufferInfo = new GRGlFramebufferInfo(0, SKColorType.Rgba8888.ToGlSizedFormat());
                _renderTarget = new GRBackendRenderTarget(width, height, 0, 8, framebufferInfo);

                // 创建GPU表面
                _surface = SKSurface.Create(_grContext, _renderTarget, GRSurfaceOrigin.BottomLeft, SKColorType.Rgba8888);
                if (_surface == null)
                {
                    System.Diagnostics.Debug.WriteLine("GPU渲染: 无法创建GPU表面");
                    return false;
                }

                _isInitialized = true;
                System.Diagnostics.Debug.WriteLine($"GPU渲染: 初始化成功 ({width}x{height})");
                LogGpuInfo();
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"GPU渲染初始化失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 调整渲染目标大小
        /// </summary>
        public bool Resize(int width, int height)
        {
            if (width == _width && height == _height)
                return true;

            _width = width;
            _height = height;

            // 重新创建表面
            _surface?.Dispose();
            _renderTarget?.Dispose();

            try
            {
                var framebufferInfo = new GRGlFramebufferInfo(0, SKColorType.Rgba8888.ToGlSizedFormat());
                _renderTarget = new GRBackendRenderTarget(width, height, 0, 8, framebufferInfo);
                _surface = SKSurface.Create(_grContext, _renderTarget, GRSurfaceOrigin.BottomLeft, SKColorType.Rgba8888);
                
                return _surface != null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"GPU渲染尺寸调整失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 开始GPU渲染
        /// </summary>
        public bool BeginRender()
        {
            if (!_isInitialized || _surface == null)
                return false;

            try
            {
                var canvas = _surface.Canvas;
                canvas.Clear(SKColors.Transparent);
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"开始GPU渲染失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 结束GPU渲染并刷新
        /// </summary>
        public void EndRender()
        {
            if (!_isInitialized || _surface == null)
                return;

            try
            {
                _surface.Canvas.Flush();
                _grContext.Flush();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"结束GPU渲染失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取渲染结果为位图
        /// </summary>
        public SKBitmap GetRenderResult()
        {
            if (!_isInitialized || _surface == null)
                return null;

            try
            {
                // 使用Snapshot方法获取图像，然后转换为位图
                using (var image = _surface.Snapshot())
                {
                    var bitmap = SKBitmap.FromImage(image);
                    return bitmap;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取GPU渲染结果失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 批量渲染实体
        /// </summary>
        public void RenderEntitiesBatch(IEnumerable<EntityBase> entities, BoundingBox viewport)
        {
            if (!BeginRender())
                return;

            var canvas = _surface.Canvas;
            
            // 设置变换矩阵
            var matrix = CalculateViewMatrix(viewport);
            canvas.SetMatrix(in matrix);

            // 批量渲染实体
            foreach (var entity in entities)
            {
                if (entity?.IsVisible == true && entity.BoundingBox != null && viewport.Intersects(entity.BoundingBox))
                {
                    // 这里需要实体支持直接到SKCanvas的渲染
                    // 由于当前实体系统使用IView接口，我们需要适配
                    try
                    {
                        var canvasView = new CanvasView(canvas);
                        entity.Render(canvasView);
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"GPU渲染实体失败: {ex.Message}");
                    }
                }
            }

            EndRender();
        }

        /// <summary>
        /// 计算视图变换矩阵
        /// </summary>
        private SKMatrix CalculateViewMatrix(BoundingBox viewport)
        {
            var scaleX = _width / (float)viewport.Width;
            var scaleY = _height / (float)viewport.Height;
            var scale = Math.Min(scaleX, scaleY);

            var translateX = -viewport.Left * scale + (_width - viewport.Width * scale) / 2;
            var translateY = -viewport.Top * scale + (_height - viewport.Height * scale) / 2;

            var scaleMatrix = SKMatrix.CreateScale((float)scale, -(float)scale);
            var translateMatrix = SKMatrix.CreateTranslation((float)translateX, (float)translateY);
            return scaleMatrix.PreConcat(translateMatrix);
        }

        /// <summary>
        /// 获取GPU渲染统计信息
        /// </summary>
        public string GetGpuStats()
        {
            if (!_isInitialized)
                return "GPU渲染未初始化";

            try
            {
                int resourceCount = 0;
                long resourceBytes = 0;
                if (_grContext != null)
                {
                    _grContext.GetResourceCacheUsage(out resourceCount, out resourceBytes);
                }
                var memoryUsage = (resourceCount, resourceBytes);
                return $"GPU内存使用: {memoryUsage.Item2 / 1024 / 1024:F1}MB, 资源数: {memoryUsage.Item1}";
            }
            catch
            {
                return "GPU统计信息不可用";
            }
        }

        public void Dispose()
        {
            _surface?.Dispose();
            _surface = null;
            
            _renderTarget?.Dispose();
            _renderTarget = null;
            
            _grContext?.Dispose();
            _grContext = null;
            
            _isInitialized = false;
        }

        /// <summary>
        /// 检查系统是否支持硬件加速
        /// </summary>
        private bool CheckHardwareAccelerationSupport()
        {
            try
            {
                // 检查WPF硬件加速支持
                var renderingTier = System.Windows.Media.RenderCapability.Tier;
                if (renderingTier == 0)
                {
                    System.Diagnostics.Debug.WriteLine("WPF硬件加速被禁用");
                    return false;
                }

                // 检查是否支持像素着色器
                var supportsPixelShader = System.Windows.Media.RenderCapability.IsPixelShaderVersionSupported(2, 0);
                if (!supportsPixelShader)
                {
                    System.Diagnostics.Debug.WriteLine("不支持像素着色器2.0");
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"检查硬件加速支持时出错: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 创建GPU上下文，包含多种后备方案
        /// </summary>
        private GRContext CreateGpuContextWithFallback()
        {
            GRContext context = null;

            try
            {
                // 方案1: 尝试创建标准OpenGL上下文
                context = GRContext.CreateGl();
                if (context != null)
                {
                    System.Diagnostics.Debug.WriteLine("GPU渲染: 使用标准OpenGL上下文");
                    return context;
                }

                // 方案2: 尝试创建带有特定接口的OpenGL上下文
                var glInterface = GRGlInterface.Create();
                if (glInterface != null)
                {
                    context = GRContext.CreateGl(glInterface);
                    if (context != null)
                    {
                        System.Diagnostics.Debug.WriteLine("GPU渲染: 使用自定义OpenGL接口");
                        return context;
                    }
                }

                System.Diagnostics.Debug.WriteLine("GPU渲染: 所有GPU上下文创建方案都失败");
                return null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"创建GPU上下文时出错: {ex.Message}");
                context?.Dispose();
                return null;
            }
        }

        /// <summary>
        /// 记录GPU信息
        /// </summary>
        private void LogGpuInfo()
        {
            try
            {
                if (_grContext != null)
                {
                    // 记录GPU上下文基本信息
                    System.Diagnostics.Debug.WriteLine("GPU渲染: GPU上下文创建成功");

                    // 尝试获取资源缓存信息
                    try
                    {
                        int resourceCount = 0;
                        long resourceBytes = 0;
                        _grContext.GetResourceCacheUsage(out resourceCount, out resourceBytes);
                        System.Diagnostics.Debug.WriteLine($"GPU渲染: 资源缓存 {resourceCount} 个对象, {resourceBytes / 1024} KB");
                    }
                    catch
                    {
                        System.Diagnostics.Debug.WriteLine("GPU渲染: 无法获取资源缓存信息");
                    }

                    // 记录渲染表面信息
                    if (_surface != null)
                    {
                        System.Diagnostics.Debug.WriteLine($"GPU渲染: 渲染表面尺寸 {_width}x{_height}");
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"记录GPU信息时出错: {ex.Message}");
            }
        }

      
    }
}
