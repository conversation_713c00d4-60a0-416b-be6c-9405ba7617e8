using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using SkiaSharp;
using McLaser.EditViewerSk.Entitys;
using McLaser.EditViewerSk.Interfaces;
using McLaser.EditViewerSk.Base;
using McLaser;

namespace McLaser.EditViewerSk.Rendering
{
    /// <summary>
    /// 高性能渲染器，结合GPU加速和多种优化技术
    /// </summary>
    public class HighPerformanceRenderer : IDisposable
    {
        private readonly object _renderLock = new object();
        private bool _useParallelRendering = true;
        private int _parallelThreshold = 50;

        public HighPerformanceRenderer()
        {
        }

        /// <summary>
        /// 渲染性能统计
        /// </summary>
        public class RenderPerformanceStats
        {
            public double TotalRenderTime { get; set; }
            public int TotalEntities { get; set; }
            public int RenderedEntities { get; set; }
            public int CulledEntities { get; set; }
            public string RenderMethod { get; set; }
            public double EntitiesPerSecond => TotalRenderTime > 0 ? RenderedEntities / (TotalRenderTime / 1000.0) : 0;

            public override string ToString()
            {
                return $"{RenderMethod}: {RenderedEntities}/{TotalEntities} 实体, " +
                       $"耗时: {TotalRenderTime:F1}ms, " +
                       $"性能: {EntitiesPerSecond:F0} 实体/秒, " +
                       $"剔除: {CulledEntities}";
            }
        }

        /// <summary>
        /// 高性能渲染图层
        /// </summary>
        public RenderPerformanceStats RenderLayer(EntityLayer layer, SKCanvas canvas, BoundingBox viewport)
        {
            var stats = new RenderPerformanceStats();
            var startTime = DateTime.Now;

            try
            {
                if (!layer.IsVisible)
                {
                    stats.RenderMethod = "跳过(不可见)";
                    return stats;
                }

                // 获取可见实体
                var allEntities = layer.Children.ToList();
                var visibleEntities = GetVisibleEntities(allEntities, viewport);

                stats.TotalEntities = allEntities.Count;
                stats.RenderedEntities = visibleEntities.Count;
                stats.CulledEntities = allEntities.Count - visibleEntities.Count;

                // 使用顺序CPU渲染
                RenderWithSequentialCpu(visibleEntities, canvas, viewport, stats);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"高性能渲染失败: {ex.Message}");
                stats.RenderMethod = "错误";
            }
            finally
            {
                stats.TotalRenderTime = (DateTime.Now - startTime).TotalMilliseconds;
            }

            return stats;
        }

        /// <summary>
        /// 获取视口内可见的实体
        /// </summary>
        private List<EntityBase> GetVisibleEntities(List<EntityBase> entities, BoundingBox viewport)
        {
            return entities.Where(entity =>
                entity?.IsVisible == true &&
                entity.BoundingBox != null &&
                !entity.BoundingBox.IsEmpty &&
                viewport.Intersects(entity.BoundingBox)
            ).ToList();
        }

        /// <summary>
        /// 顺序CPU渲染
        /// </summary>
        private void RenderWithSequentialCpu(List<EntityBase> entities, SKCanvas canvas, BoundingBox viewport, RenderPerformanceStats stats)
        {
            var canvasView = new CanvasView(canvas);

            foreach (var entity in entities)
            {
                try
                {
                    entity.Render(canvasView);
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"顺序渲染实体失败: {ex.Message}");
                }
            }

            stats.RenderMethod = "顺序CPU";
        }

        /// <summary>
        /// 设置并行渲染参数
        /// </summary>
        public void SetParallelRenderingSettings(bool enabled, int threshold = 50)
        {
            _useParallelRendering = enabled;
            _parallelThreshold = threshold;
        }

        /// <summary>
        /// 获取渲染器统计信息
        /// </summary>
        public string GetRendererStats()
        {
            return $"并行渲染: {_useParallelRendering}, " +
                   $"并行阈值: {_parallelThreshold}, " +
                   $"CPU核心数: {Environment.ProcessorCount}";
        }

        public void Dispose()
        {
        }
    }

    /// <summary>
    /// 简化的CanvasView实现
    /// </summary>
    internal class CanvasView : IView
    {
        public SKCanvas Canvas { get; }
        public DocumentBase Document { get; set; }

        public CanvasView(SKCanvas canvas)
        {
            Canvas = canvas;
        }

        public void OnMouseDoubleClick(MouseEventArgs e) { }
        public void OnCommand(McLaser.EditViewerSk.Interfaces.ICommand cmd) { }
        
        public float ModelToCanvas(double value) => (float)value;
        public Vector2 ModelToCanvas(Vector2 pointInModel) => pointInModel;
        public float CanvasToModel(double value) => (float)value;
        public Vector2 CanvasToModel(Vector2 pointInCanvas) => pointInCanvas;
        public void RepaintCanvas(bool bufferBitmapToRedraw = false) { }
        public void OnZoomFit(BoundingBox br = null) { }
        
        public void Dispose() { }
    }
}
